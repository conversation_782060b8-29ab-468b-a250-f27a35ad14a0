import { useState, useEffect } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { GardenPlant, Plant } from '@/types/plant';
import { DatabaseService, GardenCollection, PlantIdentification } from '@/services/database';
import { useAuth } from './useAuth';

// Helper function to convert GardenCollection to GardenPlant
const convertToGardenPlant = (collection: GardenCollection): GardenPlant => {
  const plantData = collection.plant_identifications;
  const diagnosisData = collection.plant_diagnoses?.[0]; // Get the most recent diagnosis

  return {
    id: collection.id,
    scientificName: plantData?.scientific_name || 'Unknown',
    commonName: plantData?.common_name || collection.nickname || 'Unknown Plant',
    imageUrl: plantData?.image_url || '',
    description: plantData?.description || '',
    careInstructions: plantData?.care_instructions || '',
    tags: plantData?.tags || [],
    addedDate: new Date(collection.created_at),
    nickname: collection.nickname,
    healthStatus: collection.health_status,
    lastWatered: collection.last_watered ? new Date(collection.last_watered) : undefined,
    lastFertilized: collection.last_fertilized ? new Date(collection.last_fertilized) : undefined,
    notes: collection.notes,
    isPublic: collection.is_public,
    diagnosis: diagnosisData ? {
      id: diagnosisData.id,
      diagnosedProblem: diagnosisData.diagnosed_problem,
      severity: diagnosisData.severity,
      immediateActions: diagnosisData.immediate_actions,
      longTermCare: diagnosisData.long_term_care,
      prognosis: diagnosisData.prognosis,
      createdAt: new Date(diagnosisData.created_at),
    } : undefined,
  };
};

export const [GardenProvider, useGarden] = createContextHook(() => {
  const [plants, setPlants] = useState<GardenPlant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // Load garden plants from database
  useEffect(() => {
    const loadGarden = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const collections = await DatabaseService.getGardenCollections(user.id);
        const gardenPlants = collections.map(convertToGardenPlant);
        setPlants(gardenPlants);
      } catch (error) {
        console.error('Error loading garden:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGarden();
  }, [user]);

  const addPlant = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: plant.careInstructions,
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: false,
      });

      if (!identification) {
        console.error('Failed to create plant identification');
        return;
      }

      // Then add to garden collection
      const gardenItem = await DatabaseService.addToGarden({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: 'healthy',
        is_public: false,
        allow_community_tips: true,
      });

      if (gardenItem) {
        const gardenPlant = convertToGardenPlant(gardenItem);
        setPlants((current) => [gardenPlant, ...current]);

        // If this plant was added from a diagnosis, link the garden collection to the diagnosis
        if (diagnosisId) {
          try {
            await DatabaseService.updatePlantDiagnosis(diagnosisId, {
              garden_collection_id: gardenItem.id,
            });
          } catch (error) {
            console.error('Error linking diagnosis to garden collection:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error adding plant to garden:', error);
    }
  };

  const removePlant = async (plantId: string) => {
    console.log('Garden store: Attempting to remove plant with ID:', plantId);
    try {
      const success = await DatabaseService.removeFromGarden(plantId);
      console.log('Garden store: Database removal result:', success);
      if (success) {
        setPlants((current) => {
          const filtered = current.filter((plant) => plant.id !== plantId);
          console.log('Garden store: Updated plants list, removed 1 item, new count:', filtered.length);
          return filtered;
        });
        return true;
      } else {
        throw new Error('Failed to remove plant from database');
      }
    } catch (error) {
      console.error('Garden store: Error removing plant from garden:', error);
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const waterPlant = async (plantId: string) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, {
        last_watered: new Date().toISOString(),
      });

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      console.error('Error updating plant watering:', error);
    }
  };

  const updatePlant = async (plantId: string, updates: Partial<GardenCollection>) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, updates);

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      console.error('Error updating plant:', error);
    }
  };

  const shareGardenItem = async (plantId: string, isPublic: boolean, shareSettings: any) => {
    try {
      const updates: Partial<GardenCollection> = {
        is_public: isPublic,
        allow_community_tips: shareSettings.allowCommunityTips,
        seo_title: shareSettings.seoTitle || undefined,
        seo_description: shareSettings.seoDescription || undefined,
        tags: shareSettings.tags.length > 0 ? shareSettings.tags : undefined,
      };

      await updatePlant(plantId, updates);
    } catch (error) {
      console.error('Error sharing garden item:', error);
      throw error;
    }
  };

  const addPlantAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: plant.careInstructions,
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: true, // Make identification public for sharing
      });

      if (!identification) {
        console.error('Failed to create plant identification');
        return;
      }

      // Then add to garden collection with sharing enabled
      const gardenItem = await DatabaseService.addToGardenAndShare({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: 'healthy',
        is_public: true, // Make garden item public for sharing
        allow_community_tips: true,
      });

      if (gardenItem) {
        const gardenPlant = convertToGardenPlant(gardenItem);
        setPlants((current) => [gardenPlant, ...current]);

        // If this plant was added from a diagnosis, link the garden collection to the diagnosis
        if (diagnosisId) {
          try {
            await DatabaseService.updatePlantDiagnosis(diagnosisId, {
              garden_collection_id: gardenItem.id,
              is_public: true, // Also make the diagnosis public
            });
          } catch (error) {
            console.error('Error linking diagnosis to garden collection:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error adding plant to garden and sharing:', error);
    }
  };

  const shareIdentificationOnly = async (plant: Plant) => {
    if (!user) return;

    try {
      await DatabaseService.shareIdentificationOnly(plant, plant.imageUrl, user.id);
    } catch (error) {
      console.error('Error sharing identification only:', error);
    }
  };

  const shareDiagnosisOnly = async (plant: Plant, diagnosisData: any) => {
    if (!user) return;

    try {
      await DatabaseService.shareDiagnosisOnly(plant, plant.imageUrl, user.id, diagnosisData);
    } catch (error) {
      console.error('Error sharing diagnosis only:', error);
    }
  };

  return {
    plants,
    isLoading,
    addPlant,
    addPlantAndShare,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    removePlant,
    waterPlant,
    updatePlant,
    shareGardenItem,
  };
});